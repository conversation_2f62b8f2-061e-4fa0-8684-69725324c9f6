import pandas as pd
import numpy as np
import os
from backtesting import Backtest
from strategies import SupportResistanceBreakoutRetestImprovedCSV

# --- Main Execution ---
if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    # backtesting.py expects: 'Open', 'High', 'Low', 'Close', 'Volume'
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased 'bt_name' (e.g. 'Open') already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        # If 'volume' (lowercase) exists, rename its original cased version to 'Volume'
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: # Check if 'Volume' (exact case) is already present
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns for backtesting.py are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    
    # Filter data for a smaller period for faster initial testing if needed
    # data = data['2022-01-01':'2022-03-31'] # Example: 3 months of data
    # print(f"Data filtered for testing. New shape: {data.shape}")

    # Filter data for testing - use just 1 week of data for faster testing
    data = data.loc['2015-01-01':'2015-04-17']
    print(f"Data filtered for testing. New shape: {data.shape}")


    # Initialize and run the backtest
    # Adjusted cash and margin: NIFTY futures lot size can be e.g. 50, price e.g. 20000
    # Margin for NIFTY futures can be around 10-15%, so 1/10 or 1/7.
    # Cash should be enough for a few lots. e.g., 50 * 20000 * 0.15 margin * 2 lots = 3,00,000
    bt = Backtest(data, SupportResistanceBreakoutRetestImprovedCSV, cash=30000, margin=1/10, commission=0.0) # Increased cash, adjusted margin
    
    print("Running backtest...")
    # Suppress UserWarnings from backtesting.py about margin if they are too noisy,
    # but it's good to see them initially.
    # import warnings
    # with warnings.catch_warnings():
    #     warnings.filterwarnings('ignore', category=UserWarning, module='backtesting.backtesting')
    stats = bt.run()
    print("\n--- Backtest Performance Report ---")
    print(stats)
    
    # Get the strategy instance
    strategy_instance = bt._strategy
    
    # Print debug info about the strategy's state
    print("\n--- Strategy State Debug ---")
    print(f"Has trade_log attribute: {hasattr(strategy_instance, 'trade_log')}")
    if hasattr(strategy_instance, 'trade_log'):
        print(f"Trade log length: {len(strategy_instance.trade_log)}")
    
    print(f"Has current_trade_entry_details attribute: {hasattr(strategy_instance, 'current_trade_entry_details')}")
    if hasattr(strategy_instance, 'current_trade_entry_details'):
        print(f"Current trade entry details: {strategy_instance.current_trade_entry_details}")
    
    # Manually close any open trades to ensure the trade log is complete
    if hasattr(strategy_instance, 'close_all_trades'):
        try:
            # Call the method without 'self' parameter
            strategy_instance.close_all_trades.__func__(strategy_instance)
            print("Successfully closed all trades")
        except Exception as e:
            print(f"Error closing trades: {e}")

    # Detailed Metrics Calculation (some might be redundant with stats output but good for clarity)
    print(f"\n--- Detailed Metrics ---")
    print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
    if 'Return (Ann.) [%]' in stats: # May not be present if data < 1 year
        print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
    else:
        print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
    print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
    print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f} (Risk-free rate assumed 0%)")
    print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
    print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
    print(f"Profit Factor:                    {stats['Profit Factor']:.2f}")
    print(f"Total Number of Trades:           {stats['# Trades']}")
    
    # Debug: Print the first few trades to understand the structure
    all_trades = stats['_trades']
    if not all_trades.empty and len(all_trades) > 0:
        print("\n--- First Trade Details (Debug) ---")
        first_trade = all_trades.iloc[0]
        for col in first_trade.index:
            print(f"{col}: {first_trade[col]}")
    
    all_trades = stats['_trades']
    if not all_trades.empty:
        avg_trade_pnl_points = all_trades['PnL'].mean() # PnL is in cash, need to convert if points needed
        
        # For NIFTY, PnL is already in points if 1 unit is traded.
        # If PnL is cash and you need points, you'd divide by lot size * point value.
        # Assuming PnL from backtesting.py with 1 unit trade is effectively in points for NIFTY.
        print(f"Average Trade PnL (points):       {all_trades['PnL'].mean():.2f}")
        
        winning_trades = all_trades[all_trades['PnL'] > 0]
        losing_trades = all_trades[all_trades['PnL'] < 0]
        
        avg_winning_trade_points = winning_trades['PnL'].mean() if not winning_trades.empty else 0
        avg_losing_trade_points = losing_trades['PnL'].mean() if not losing_trades.empty else 0
        
        print(f"Average Winning Trade (points):   {avg_winning_trade_points:.2f}")
        print(f"Average Losing Trade (points):    {avg_losing_trade_points:.2f}")
        
        # Average Trade Duration (in bars)
        avg_trade_duration_bars = all_trades['Duration'].mean()
        print(f"Average Trade Duration (bars):    {avg_trade_duration_bars}")
        # To convert to time, you'd need the frequency of your data (e.g., 1 bar = 1 minute)
        # avg_trade_duration_time = pd.to_timedelta(avg_trade_duration_bars * pd.Timedelta(minutes=1)) # Example for 1-min data
        # print(f"Average Trade Duration (time):    {avg_trade_duration_time}")

    else:
        print("No trades were executed.")
        
    # Export trade log to CSV
    print("\n--- Exporting Trade Log to CSV ---")
    
    # Get the strategy instance to access the trade_log
    strategy_instance = bt._strategy
    
    # We don't need to manually call close_all_trades as it's a method that should be 
    # called by the backtesting framework, not directly
    
    # First, try to use the strategy's internal trade log if it exists and has entries
    strategy_has_trade_log = hasattr(strategy_instance, 'trade_log') and strategy_instance.trade_log
    
    # Print debug info about the trade log
    if strategy_has_trade_log:
        print(f"Strategy has internal trade log with {len(strategy_instance.trade_log)} entries")
        if len(strategy_instance.trade_log) > 0:
            print("First entry in trade log:")
            for key, value in strategy_instance.trade_log[0].items():
                print(f"  {key}: {value}")
    else:
        print("Strategy does not have an internal trade log or it's empty")
    
    # If the strategy has a trade log with entries, use it
    if strategy_has_trade_log and len(strategy_instance.trade_log) > 0:
        # Create a DataFrame from the trade log
        trades_df = pd.DataFrame(strategy_instance.trade_log)
        
        # Ensure the DataFrame has all the required columns
        required_columns = [
            "Index", "Entry DateTime", "Exit DateTime", "Entry Price", "Exit Price", 
            "Profit Points", "Profit Percent", "Position", "Trade Duration", "Exit Reason"
        ]
        
        # Reorder columns to match the required format
        trades_df = trades_df[required_columns]
        
        # Define the output file path
        output_dir = os.path.dirname(os.path.abspath(__file__))
        output_file = os.path.join(output_dir, "all_trades_outcome.csv")
        
        # Export to CSV
        trades_df.to_csv(output_file, index=False)
        print(f"Trade log exported to: {output_file} (using strategy's internal trade log)")
        print(f"Total trades exported: {len(trades_df)}")
    else:
        # If no trade log is available or it's empty, create one from the stats._trades DataFrame
        print("Using backtesting framework's trade data instead")
        all_trades = stats['_trades']
        if not all_trades.empty:
            # Create a new DataFrame with the required columns
            trades_list = []
            for idx, trade in all_trades.iterrows():
                entry_time = trade.EntryTime
                exit_time = trade.ExitTime
                entry_price = trade.EntryPrice
                exit_price = trade.ExitPrice
                # Get the raw PnL from the trade
                raw_pnl = trade.PnL
                position_type = "LONG" if trade.Size > 0 else "SHORT"
                duration = exit_time - entry_time
                
                # Calculate profit in points (price difference)
                if position_type == "LONG":
                    profit_points = exit_price - entry_price
                else:  # SHORT
                    profit_points = entry_price - exit_price
                
                # Calculate profit percent based on price difference
                profit_percent = (profit_points / entry_price) * 100 if entry_price != 0 else 0
                
                # Determine exit reason by comparing exit price with SL/TP
                exit_reason = "Unknown"
                
                # Check if SL and TP are set
                has_sl = hasattr(trade, 'SL') and not pd.isna(trade.SL)
                has_tp = hasattr(trade, 'TP') and not pd.isna(trade.TP)
                
                # Use a larger tolerance for price comparisons (0.1% of price)
                tolerance = exit_price * 0.001
                
                # Check if exit time is near market close (after 15:15)
                exit_time_str = exit_time.strftime('%H:%M')
                if exit_time_str >= "15:15":
                    exit_reason = "Time-based Exit"
                elif has_sl and has_tp:
                    if position_type == "LONG":
                        # For LONG positions
                        if abs(exit_price - trade.SL) <= tolerance:
                            exit_reason = "Stop Loss"
                        elif abs(exit_price - trade.TP) <= tolerance:
                            exit_reason = "Take Profit"
                        # Check if exit price is closer to SL than to entry price
                        elif exit_price < entry_price and abs(exit_price - trade.SL) < abs(exit_price - entry_price):
                            exit_reason = "Stop Loss"
                        # Check if exit price is closer to TP than to entry price
                        elif exit_price > entry_price and abs(exit_price - trade.TP) < abs(exit_price - entry_price):
                            exit_reason = "Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                    else:  # SHORT position
                        # For SHORT positions
                        if abs(exit_price - trade.SL) <= tolerance:
                            exit_reason = "Stop Loss"
                        elif abs(exit_price - trade.TP) <= tolerance:
                            exit_reason = "Take Profit"
                        # Check if exit price is closer to SL than to entry price
                        elif exit_price > entry_price and abs(exit_price - trade.SL) < abs(exit_price - entry_price):
                            exit_reason = "Stop Loss"
                        # Check if exit price is closer to TP than to entry price
                        elif exit_price < entry_price and abs(exit_price - trade.TP) < abs(exit_price - entry_price):
                            exit_reason = "Take Profit"
                        else:
                            exit_reason = "Manual/Other Exit"
                elif has_sl:
                    # Only SL is set
                    if (position_type == "LONG" and exit_price <= trade.SL + tolerance) or \
                       (position_type == "SHORT" and exit_price >= trade.SL - tolerance):
                        exit_reason = "Stop Loss"
                    else:
                        exit_reason = "Manual/Other Exit"
                elif has_tp:
                    # Only TP is set
                    if (position_type == "LONG" and exit_price >= trade.TP - tolerance) or \
                       (position_type == "SHORT" and exit_price <= trade.TP + tolerance):
                        exit_reason = "Take Profit"
                    else:
                        exit_reason = "Manual/Other Exit"
                
                trades_list.append({
                    "Index": idx + 1,
                    "Entry DateTime": entry_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Exit DateTime": exit_time.strftime('%Y-%m-%d %H:%M:%S'),
                    "Entry Price": round(entry_price, 2),
                    "Exit Price": round(exit_price, 2),
                    "Profit Points": round(profit_points, 2),
                    "Profit Percent": round(profit_percent, 2),
                    "Position": position_type,
                    "Trade Duration": str(duration),
                    "Exit Reason": exit_reason
                })
            
            # Create DataFrame and export to CSV
            trades_df = pd.DataFrame(trades_list)
            
            # Define the output file path
            output_dir = os.path.dirname(os.path.abspath(__file__))
            output_file = os.path.join(output_dir, "all_trades_outcome.csv")
            
            # Export to CSV
            trades_df.to_csv(output_file, index=False)
            print(f"Trade log exported to: {output_file}")
            print(f"Total trades exported: {len(trades_df)}")
        else:
            print("No trades were executed, so no trade log is available to export.")
