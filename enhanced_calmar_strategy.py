import pandas as pd
import numpy as np
import datetime as dt
import csv
import matplotlib.pyplot as plt
from backtesting import Backtest, Strategy
from backtesting.lib import crossover, cross
from backtesting.test import SMA

# ===== INDICATOR FUNCTIONS =====

def ATR(high, low, close, n=14):
    """
    Calculate Average True Range (ATR)
    """
    tr1 = pd.Series(high) - pd.Series(low)
    tr2 = abs(pd.Series(high) - pd.Series(close).shift(1))
    tr3 = abs(pd.Series(low) - pd.Series(close).shift(1))
    
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    atr = tr.rolling(n).mean().values
    
    return atr

def BB(array, n, std_dev):
    """
    Calculate Bollinger Bands
    """
    # Calculate the middle band (SMA)
    middle = pd.Series(array).rolling(n).mean().values
    
    # Calculate the standard deviation
    std = pd.Series(array).rolling(n).std().values
    
    # Calculate the upper and lower bands
    upper = middle + std_dev * std
    lower = middle - std_dev * std
    
    return upper, middle, lower

def RSI(array, n=14):
    """
    Calculate Relative Strength Index (RSI)
    """
    # Convert to pandas series for easier calculation
    close_delta = pd.Series(array).diff()
    
    # Make two series: one for gains and one for losses
    up = close_delta.clip(lower=0)
    down = -1 * close_delta.clip(upper=0)
    
    # Calculate the EWMA (Exponential Weighted Moving Average)
    ma_up = up.ewm(com=n-1, adjust=True, min_periods=n).mean()
    ma_down = down.ewm(com=n-1, adjust=True, min_periods=n).mean()
    
    # Calculate the RSI
    rs = ma_up / ma_down
    rsi = 100 - (100 / (1 + rs))
    
    return rsi.values

def VWAP(high, low, close, volume, n=20):
    """
    Calculate Volume Weighted Average Price (VWAP)
    """
    typical_price = (high + low + close) / 3
    tp_volume = typical_price * volume
    
    cumulative_tp_volume = pd.Series(tp_volume).rolling(n).sum()
    cumulative_volume = pd.Series(volume).rolling(n).sum()
    
    vwap = (cumulative_tp_volume / cumulative_volume).values
    
    return vwap

def is_time_between(time_str, start_time_str, end_time_str):
    """
    Check if a time string is between start and end time strings
    """
    time = dt.datetime.strptime(time_str, "%H:%M").time()
    start_time = dt.datetime.strptime(start_time_str, "%H:%M").time()
    end_time = dt.datetime.strptime(end_time_str, "%H:%M").time()
    return start_time <= time <= end_time

# ===== STRATEGY CLASS =====

class EnhancedCalmarStrategy(Strategy):
    """
    Enhanced strategy focused on achieving high Calmar Ratio through:
    1. Advanced mean reversion with volatility-adjusted entries/exits
    2. Robust risk management with adaptive stop losses
    3. Time-based constraints for intraday trading
    4. Daily loss limits to preserve capital
    """
    
    # Strategy parameters (optimizable)
    bb_window = 10          # Bollinger Band period - shorter for intraday
    bb_std = 1.5            # Bollinger Band standard deviation - tighter for more signals
    atr_period = 10         # ATR period for volatility measurement
    rsi_period = 7          # RSI period - shorter for intraday
    rsi_oversold = 40       # RSI oversold threshold - less extreme for more signals
    rsi_overbought = 60     # RSI overbought threshold - less extreme for more signals
    vwap_period = 10        # VWAP period - shorter for intraday
    
    # Risk management parameters
    risk_per_trade = 0.01   # 1% risk per trade
    atr_stop_multiplier = 2.0  # Stop loss at 2x ATR
    max_daily_loss = 0.02   # 2% maximum daily loss
    max_trades_per_day = 10  # Maximum trades per day
    
    # Time constraints
    market_start_time = "09:15"
    market_end_time = "15:30"
    entry_start_time = "09:30"  # Allow time for indicators to stabilize
    exit_end_time = "15:15"     # Force close all trades before market close
    
    # Trade tracking
    trade_records = []
    current_trade = None
    daily_pnl = 0.0
    daily_trade_count = 0
    current_day = None
    
    def init(self):
        """Initialize strategy indicators and variables"""
        # Price-based indicators
        self.bb_upper, self.bb_mid, self.bb_lower = self.I(BB, self.data.Close, self.bb_window, self.bb_std)
        self.atr = self.I(ATR, self.data.High, self.data.Low, self.data.Close, self.atr_period)
        self.rsi = self.I(RSI, self.data.Close, self.rsi_period)
        
        # Volume-based indicator (using dummy volume if necessary)
        self.vwap = self.I(VWAP, self.data.High, self.data.Low, self.data.Close, self.data.Volume, self.vwap_period)
        
        # Initialize trade tracking
        self.trade_records = []
        self.current_trade = None
        self.daily_pnl = 0.0
        self.daily_trade_count = 0
        self.current_day = None
        
        # Store stop loss levels
        self.stop_loss = None
        
        # Remove the problematic line
        # We'll use the built-in equity curve plotting instead
    
    def next(self):
        """Main strategy logic executed on each bar"""
        # Skip trading until we have enough data for all indicators
        if (np.isnan(self.bb_lower[-1]) or np.isnan(self.bb_upper[-1]) or 
            np.isnan(self.atr[-1]) or np.isnan(self.rsi[-1]) or np.isnan(self.vwap[-1])):
            return
            
        # Debug - print indicator values every 100 bars
        if len(self.data) % 100 == 0:
            print(f"Bar: {len(self.data)}, Close: {self.data.Close[-1]:.2f}, BB Lower: {self.bb_lower[-1]:.2f}, BB Upper: {self.bb_upper[-1]:.2f}, RSI: {self.rsi[-1]:.2f}, ATR: {self.atr[-1]:.2f}")
        
        # Get current time and date information
        current_datetime = self.data.index[-1]
        current_time = current_datetime.strftime("%H:%M")
        current_day = current_datetime.date()
        
        # Reset daily counters if a new day has started
        if self.current_day is None or current_day != self.current_day:
            self.daily_pnl = 0.0
            self.daily_trade_count = 0
            self.current_day = current_day
        
        # Force close all positions at exit_end_time
        if is_time_between(current_time, self.exit_end_time, self.market_end_time) and self.position:
            if self.current_trade:
                self.current_trade["exit_reason"] = "EOD_CLOSE"
            self.position.close()
            return
        
        # Check for stop loss if in a position
        if self.position and self.stop_loss is not None:
            if (self.position.is_long and self.data.Low[-1] <= self.stop_loss) or \
               (self.position.is_short and self.data.High[-1] >= self.stop_loss):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "STOP_LOSS"
                self.position.close()
                return
        
        # Only enter new positions between entry_start_time and exit_end_time
        # and if we haven't hit daily limits
        if not is_time_between(current_time, self.entry_start_time, self.exit_end_time):
            if len(self.data) % 100 == 0:
                print(f"Time filter: Current time {current_time} is outside trading window {self.entry_start_time}-{self.exit_end_time}")
            return
        
        # Check if we've hit daily loss limit or max trades per day
        if self.daily_pnl <= -self.max_daily_loss * self.equity or self.daily_trade_count >= self.max_trades_per_day:
            return
        
        # Calculate position size based on risk
        price = self.data.Close[-1]
        atr_value = self.atr[-1]
        
        # Skip if ATR is too low (illiquid or low volatility) - lowered threshold
        if atr_value < price * 0.0001:  # Minimum volatility threshold
            return
        
        # Calculate position size based on risk per trade and ATR
        risk_amount = self.equity * self.risk_per_trade
        stop_distance = atr_value * self.atr_stop_multiplier
        position_size = max(1, int(risk_amount / stop_distance))  # At least 1 unit
        
        # Trading logic - only enter if not in a position
        if not self.position:
            # LONG ENTRY CONDITIONS:
            # 1. Price is near or below lower Bollinger Band (oversold)
            # 2. RSI is below oversold threshold OR price is below VWAP
            if (self.data.Close[-1] <= self.bb_lower[-1] * 1.01 and  # Allow slight buffer
                (self.rsi[-1] < self.rsi_oversold or self.data.Close[-1] < self.vwap[-1])):
                
                # Calculate stop loss level
                self.stop_loss = self.data.Close[-1] - stop_distance
                
                # Enter long position
                self.buy(size=position_size)
                self.daily_trade_count += 1
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": self.data.Close[-1],
                    "position": "LONG",
                    "stop_loss": self.stop_loss,
                    "position_size": position_size,
                    "exit_reason": "OPEN"
                }
            
            # SHORT ENTRY CONDITIONS:
            # 1. Price is near or above upper Bollinger Band (overbought)
            # 2. RSI is above overbought threshold OR price is above VWAP
            elif (self.data.Close[-1] >= self.bb_upper[-1] * 0.99 and  # Allow slight buffer
                  (self.rsi[-1] > self.rsi_overbought or self.data.Close[-1] > self.vwap[-1])):
                
                # Calculate stop loss level
                self.stop_loss = self.data.Close[-1] + stop_distance
                
                # Enter short position
                self.sell(size=position_size)
                self.daily_trade_count += 1
                
                # Record trade information
                self.current_trade = {
                    "entry_datetime": current_datetime,
                    "entry_price": self.data.Close[-1],
                    "position": "SHORT",
                    "stop_loss": self.stop_loss,
                    "position_size": position_size,
                    "exit_reason": "OPEN"
                }
        
        # Exit conditions for existing positions
        elif self.position:
            # Calculate current profit percentage
            if self.position.is_long:
                profit_pct = (self.data.Close[-1] - self.current_trade["entry_price"]) / self.current_trade["entry_price"] * 100
            else:
                profit_pct = (self.current_trade["entry_price"] - self.data.Close[-1]) / self.current_trade["entry_price"] * 100
            
            # Take profit at 0.5% gain (scalping approach)
            if profit_pct >= 0.5:
                if self.current_trade:
                    self.current_trade["exit_reason"] = "TAKE_PROFIT"
                self.position.close()
                return
            
            # Exit long position when:
            # 1. Price crosses above VWAP, or
            # 2. RSI moves above 50 (neutral), or
            # 3. We've been in the trade for more than 30 minutes
            if self.position.is_long and (
                self.data.Close[-1] > self.vwap[-1] or 
                self.rsi[-1] > 50 or
                (current_datetime - self.current_trade["entry_datetime"]).total_seconds() > 1800
            ):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "SIGNAL_EXIT"
                self.position.close()
            
            # Exit short position when:
            # 1. Price crosses below VWAP, or
            # 2. RSI moves below 50 (neutral), or
            # 3. We've been in the trade for more than 30 minutes
            elif self.position.is_short and (
                self.data.Close[-1] < self.vwap[-1] or 
                self.rsi[-1] < 50 or
                (current_datetime - self.current_trade["entry_datetime"]).total_seconds() > 1800
            ):
                if self.current_trade:
                    self.current_trade["exit_reason"] = "SIGNAL_EXIT"
                self.position.close()
    
    def on_trade(self, trade):
        """Called when a trade is closed"""
        if self.current_trade is not None:
            # Calculate trade metrics
            exit_datetime = self.data.index[-1]
            exit_price = trade.exit_price
            exit_reason = self.current_trade.get("exit_reason", "UNKNOWN")
            
            # Calculate profit
            profit_points = exit_price - self.current_trade["entry_price"] if self.current_trade["position"] == "LONG" else self.current_trade["entry_price"] - exit_price
            profit_percent = (profit_points / self.current_trade["entry_price"]) * 100
            
            # Update daily P&L tracking
            trade_pnl_ratio = profit_percent / 100
            position_size_ratio = self.current_trade["position_size"] / self.equity
            self.daily_pnl += trade_pnl_ratio * position_size_ratio
            
            # Calculate trade duration
            duration = exit_datetime - self.current_trade["entry_datetime"]
            duration_str = str(duration)
            
            # Create trade record
            trade_record = {
                "index": len(self.trade_records) + 1,
                "entry_datetime": self.current_trade["entry_datetime"],
                "exit_datetime": exit_datetime,
                "entry_price": self.current_trade["entry_price"],
                "exit_price": exit_price,
                "stop_loss": self.current_trade["stop_loss"],
                "exit_reason": exit_reason,
                "profit_points": profit_points,
                "profit_percent": profit_percent,
                "position": self.current_trade["position"],
                "position_size": self.current_trade["position_size"],
                "trade_duration": duration_str,
                "exit_reason": exit_reason
            }
            
            # Add to trade records
            self.trade_records.append(trade_record)
            self.current_trade = None
            self.stop_loss = None

# ===== MAIN EXECUTION =====

if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date'
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming)
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        else:
            # Check if the correctly cased name already exists
            if bt_name not in data.columns:
                 print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
                 exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present
    if 'volume' in column_map_lower:
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns:
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    # Filter data for testing - adjust date range as needed
    # For initial testing, use a smaller dataset
    data = data.loc['2015-01-01':'2015-03-01']
    print(f"Data filtered for testing. New shape: {data.shape}")
    
    # Add slippage and commission for realistic modeling
    commission = 0.0005  # 0.05% commission per trade
    
    # Create and run backtest
    bt = Backtest(data, EnhancedCalmarStrategy, cash=100000, commission=commission)
    stats = bt.run()

    # Print the stats
    print("\n===== PERFORMANCE METRICS =====")
    print(stats[['Start', 'End', 'Duration', 'Exposure Time [%]',
                'Equity Final [$]', 'Equity Peak [$]', 'Return [%]',
                'Buy & Hold Return [%]', 'Max. Drawdown [%]', '# Trades',
                'Win Rate [%]', 'Best Trade [%]', 'Worst Trade [%]',
                'Avg. Trade [%]', 'Max. Trade Duration', 'Avg. Trade Duration']])
    
    # Calculate and print Calmar Ratio
    equity = stats['_equity_curve']['Equity']
    returns = equity.pct_change().dropna()
    drawdown = 1 - equity / equity.cummax()
    max_drawdown = drawdown.max()
    annual_return = returns.mean() * 252  # Assuming 252 trading days in a year
    calmar_ratio = annual_return / max_drawdown if max_drawdown > 0 else float('inf')
    
    print(f"\n===== RISK-ADJUSTED METRICS =====")
    print(f"Annualized Return: {annual_return:.4f}")
    print(f"Maximum Drawdown: {max_drawdown:.4f}")
    print(f"Calmar Ratio: {calmar_ratio:.4f}")
    print(f"Sharpe Ratio: {stats['Sharpe Ratio']:.4f}")
    print(f"Sortino Ratio: {stats['Sortino Ratio']:.4f}")
    
    # Save trade records to CSV
    strategy_instance = bt._strategy
    if hasattr(strategy_instance, 'trade_records') and strategy_instance.trade_records:
        output_file = 'C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\enhanced_trade_records.csv'
        
        # Create a list to store processed trade records
        trade_records = []
        
        # Process each trade from our custom records
        for trade in strategy_instance.trade_records:
            trade_record = {
                'Index': trade['index'],
                'Entry DateTime': trade['entry_datetime'],
                'Exit DateTime': trade['exit_datetime'],
                'Entry Price': trade['entry_price'],
                'Exit Price': trade['exit_price'],
                'Exit Reason': trade['exit_reason'],
                'Stop Loss': trade['stop_loss'],
                'Profit Points': trade['profit_points'],
                'Profit Percent': trade['profit_percent'],
                'Position': trade['position'],
                'Position Size': trade['position_size'],
                'Trade Duration': trade['trade_duration'],
                'Exit Reason': trade['exit_reason']  # Duplicate field as requested
            }
            
            trade_records.append(trade_record)
        
        # Define CSV headers
        headers = [
            'Index', 'Entry DateTime', 'Exit DateTime', 'Entry Price', 'Exit Price',
            'Exit Reason', 'Stop Loss', 'Profit Points', 'Profit Percent', 
            'Position', 'Position Size', 'Trade Duration', 'Exit Reason'
        ]
        
        # Write to CSV
        with open(output_file, 'w', newline='') as csvfile:
            writer = csv.DictWriter(csvfile, fieldnames=headers)
            writer.writeheader()
            
            for trade in trade_records:
                writer.writerow(trade)
        
        print(f"\nSaved {len(trade_records)} trade records to {output_file}")
    else:
        print("\nNo trades were executed during the backtest.")
    
    # Plot the equity curve and drawdowns
    plt.figure(figsize=(12, 8))
    
    # Plot equity curve
    plt.subplot(2, 1, 1)
    plt.plot(equity)
    plt.title('Equity Curve')
    plt.grid(True)
    
    # Plot drawdowns
    plt.subplot(2, 1, 2)
    plt.fill_between(drawdown.index, drawdown.values)
    plt.title('Drawdowns')
    plt.grid(True)
    
    plt.tight_layout()
    plt.savefig('C:\\Users\\<USER>\\Desktop\\Python\\SimpleStrategies\\equity_curve.png')
    print("Equity curve and drawdowns plot saved to equity_curve.png")
    
    # Show the interactive plot
    bt.plot()