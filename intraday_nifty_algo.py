import pandas as pd
import numpy as np
from backtesting import Backtest, Strategy
from backtesting.lib import crossover # Not strictly needed for this strategy, but often useful

from strategies import SupportResistanceBreakoutRetest

# Define the Support/Resistance Breakout/Retest strategy
class SupportResistanceBreakoutRetest(Strategy):
    # Configuration
    n_periods = 20  # For S/R calculation
    sl_points = 10  # Stop-loss in NIFTY50 points
    tp_rr = 2       # Take-profit risk-reward ratio

    def init(self):
        # Pre-calculate Support and Resistance levels
        # Ensure min_periods=1 to get values even at the start of the data
        self.resistance = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).max(), self.data.High, name="resistance")
        self.support = self.I(lambda x: pd.Series(x).rolling(self.n_periods, min_periods=1).min(), self.data.Low, name="support")

        # Variables to track breakout and retest states
        self.breakout_up_level = np.nan
        self.breakout_down_level = np.nan
        self.retest_candle_idx = -1       # Index of the candle that touched the level for retest
        self.retest_touch_candle_high = np.nan # High of the retest touch candle
        self.retest_touch_candle_low = np.nan  # Low of the retest touch candle
        self.waiting_for_confirmation = False # Flag to indicate we are waiting for a PA confirmation candle

    def next(self):
        current_idx = len(self.data.Close) - 1
        current_close = self.data.Close[-1]
        current_high = self.data.High[-1]
        current_low = self.data.Low[-1]
        
        # Ensure we have enough data for previous candle access
        if len(self.data.Close) < 2:
            return

        previous_close = self.data.Close[-2]
        
        # --- State Reset & Breakout Detection ---
        # Upward breakout: previous close was below/at resistance, current close is above
        if not np.isnan(self.resistance[-2]) and previous_close <= self.resistance[-2] and current_close > self.resistance[-2]:
            self.breakout_up_level = self.resistance[-2] # The broken resistance level
            self.breakout_down_level = np.nan # Reset downward breakout state
            self.retest_candle_idx = -1       # Reset retest candle index
            self.waiting_for_confirmation = False # Reset confirmation flag on new breakout
            # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Upward Breakout detected at {self.breakout_up_level:.2f}\n")

        # Downward breakout: previous close was above/at support, current close is below
        elif not np.isnan(self.support[-2]) and previous_close >= self.support[-2] and current_close < self.support[-2]:
            self.breakout_down_level = self.support[-2] # The broken support level
            self.breakout_up_level = np.nan   # Reset upward breakout state
            self.retest_candle_idx = -1         # Reset retest candle index
            self.waiting_for_confirmation = False # Reset confirmation flag on new breakout
            # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Downward Breakout detected at {self.breakout_down_level:.2f}\n")

        # --- Retest Confirmation & Entry Logic ---
        # Long Entry: Retest of broken resistance (now acting as support)
        if not np.isnan(self.breakout_up_level) and not self.position: # If an upward breakout happened and no position open
            # 1. Price Action: Current candle touches the breakout_up_level (potential retest)
            if not self.waiting_for_confirmation and current_low <= self.breakout_up_level and current_high >= self.breakout_up_level:
                self.retest_candle_idx = current_idx
                self.retest_touch_candle_high = current_high # Store H/L of retest touch candle
                self.retest_touch_candle_low = current_low
                self.waiting_for_confirmation = True
                # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Potential retest of UP breakout at {self.breakout_up_level:.2f}. Touched by candle {current_idx}. Waiting for PA confirmation.\n")
            
            # 2. Price Action: Confirmation candle logic
            elif self.waiting_for_confirmation and self.retest_candle_idx == current_idx - 1:
                # Current candle is the one *after* the retest touch candle
                if current_close > self.retest_touch_candle_high: # PA Confirmation: Closes above high of retest touch candle
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - PA Confirmed for LONG. Entry candle {current_idx} closed > {self.retest_touch_candle_high:.2f}.\n")
                    sl = self.retest_touch_candle_low - self.sl_points # SL below low of retest touch candle
                    tp = current_close + (current_close - sl) * self.tp_rr
                    self.buy(sl=sl, tp=tp, size=1)
                    self.breakout_up_level = np.nan
                    self.waiting_for_confirmation = False
                    self.retest_candle_idx = -1
                else:
                    # PA Confirmation failed for long 
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - PA Confirmation FAILED for LONG. Resetting retest state.\n")
                    self.waiting_for_confirmation = False 
                    self.retest_candle_idx = -1


        # Short Entry: Retest of broken support (now acting as resistance)
        elif not np.isnan(self.breakout_down_level) and not self.position:
            # 1. Price Action: Current candle touches the breakout_down_level
            if not self.waiting_for_confirmation and current_high >= self.breakout_down_level and current_low <= self.breakout_down_level:
                self.retest_candle_idx = current_idx
                self.retest_touch_candle_high = current_high
                self.retest_touch_candle_low = current_low
                self.waiting_for_confirmation = True
                # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Potential retest of DOWN breakout at {self.breakout_down_level:.2f}. Touched by candle {current_idx}. Waiting for PA confirmation.\n")

            # 2. Price Action: Confirmation candle logic
            elif self.waiting_for_confirmation and self.retest_candle_idx == current_idx - 1:
                # Current candle is the one *after* the retest touch candle
                if current_close < self.retest_touch_candle_low: # PA Confirmation: Closes below low of retest touch candle
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - PA Confirmed for SHORT. Entry candle {current_idx} closed < {self.retest_touch_candle_low:.2f}.\n")
                    sl = self.retest_touch_candle_high + self.sl_points # SL above high of retest touch candle
                    tp = current_close - (sl - current_close) * self.tp_rr
                    self.sell(sl=sl, tp=tp, size=1)
                    self.breakout_down_level = np.nan
                    self.waiting_for_confirmation = False
                    self.retest_candle_idx = -1
                else:
                    # PA Confirmation failed for short
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - PA Confirmation FAILED for SHORT. Resetting retest state.\n")
                    self.waiting_for_confirmation = False
                    self.retest_candle_idx = -1
        
        # If waiting for confirmation and price moves too far away from breakout level without confirming, reset.
        if self.waiting_for_confirmation and current_idx > self.retest_candle_idx + 1: # If more than 1 bar passed after retest touch
            reset_confirmation = False
            if not np.isnan(self.breakout_up_level):
                 # And price is no longer near the breakout level (e.g., moved significantly below for a long setup)
                if current_high < self.breakout_up_level - (self.sl_points * 0.5): # Example threshold
                    reset_confirmation = True
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Long PA confirmation wait timed out / price moved away. Resetting.\n")
            elif not np.isnan(self.breakout_down_level):
                if current_low > self.breakout_down_level + (self.sl_points * 0.5): # Example threshold
                    reset_confirmation = True
                    # sys.stderr.write(f"DEBUG: {self.data.index[-1]} - Short PA confirmation wait timed out / price moved away. Resetting.\n")
            
            if reset_confirmation:
                self.waiting_for_confirmation = False
                self.retest_candle_idx = -1
                # Optionally reset breakout levels if they are considered stale after a failed confirmation
                # self.breakout_up_level = np.nan
                # self.breakout_down_level = np.nan
                

# --- Main Execution ---
if __name__ == '__main__':
    # Load the data
    file_path = 'C:\\Users\\<USER>\\Desktop\\Python\\data\\NIFTY_1min.csv'
    try:
        data = pd.read_csv(
            file_path,
            parse_dates=['date'],
            index_col='date',
            nrows=952000 # Using a subset for faster testing, remove/adjust for full dataset
        )
    except FileNotFoundError:
        print(f"Error: Data file not found at {file_path}")
        exit()
    except Exception as e:
        print(f"Error loading data: {e}")
        exit()

    # Ensure correct column names (case-insensitive matching and renaming for backtesting.py)
    # backtesting.py expects: 'Open', 'High', 'Low', 'Close', 'Volume'
    column_map_lower = {col.lower(): col for col in data.columns}
    
    rename_dict = {}
    # Map standard OHLC names first
    for std_name_lower, bt_name in {'open': 'Open', 'high': 'High', 'low': 'Low', 'close': 'Close'}.items():
        if std_name_lower in column_map_lower:
            rename_dict[column_map_lower[std_name_lower]] = bt_name
        elif bt_name not in data.columns: # Check if already correctly cased
             print(f"Error: Required column '{std_name_lower}' (for {bt_name}) not found in CSV header: {data.columns.tolist()}")
             exit()
            
    data.rename(columns=rename_dict, inplace=True)

    # Handle 'Volume' - create dummy if not present as backtesting.py might expect it
    if 'volume' in column_map_lower:
        data.rename(columns={column_map_lower['volume']: 'Volume'}, inplace=True)
    elif 'Volume' not in data.columns: 
        print("Warning: 'Volume' column not found. Creating a dummy 'Volume' column with zeros.")
        data['Volume'] = 0
    
    # Verify final column names
    expected_bt_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
    missing_cols = [col for col in expected_bt_cols if col not in data.columns]
    if missing_cols:
        print(f"Error: After processing, the following required columns for backtesting.py are missing: {missing_cols}. Current columns: {data.columns.tolist()}")
        exit()

    print(f"Data loaded and processed. Shape: {data.shape}")
    print(f"Data head:\n{data.head()}")
    
    # Initialize and run the backtest
    # Adjusted cash and margin for NIFTY-like instrument.
    # Commission is an estimate, adjust as per your broker.
    bt = Backtest(data, SupportResistanceBreakoutRetest, cash=30000, margin=1/10, commission=0.0)
    
    print("Running backtest for SupportResistanceBreakoutRetest strategy...")
    stats = bt.run()
    print("\n--- Backtest Performance Report ---")
    print(stats)

    # Detailed Metrics Calculation (some might be redundant with stats output but good for clarity)
    if stats is not None and '_trades' in stats and not stats['_trades'].empty:
        print(f"\n--- Detailed Metrics ---")
        print(f"Total Return (%):                 {stats['Return [%]']:.2f}%")
        if 'Return (Ann.) [%]' in stats: 
            print(f"Annualized Return (%):            {stats['Return (Ann.) [%]']:.2f}%")
        else:
            print(f"Annualized Return (%):            N/A (duration < 1 year or not calculated)")
        print(f"Max Drawdown (%):                 {stats['Max. Drawdown [%]']:.2f}%")
        print(f"Sharpe Ratio (annualized):        {stats['Sharpe Ratio']:.2f} (Risk-free rate assumed 0%)")
        print(f"Sortino Ratio (annualized):       {stats['Sortino Ratio']:.2f}")
        print(f"Win Rate (%):                     {stats['Win Rate [%]']:.2f}%")
        # Robust Profit Factor display
        pf_val = stats['Profit Factor']
        if pf_val is not None and isinstance(pf_val, (int, float)) and not np.isnan(pf_val):
            profit_factor_display = f"{pf_val:.2f}"
        else:
            profit_factor_display = 'N/A'
        print(f"Profit Factor:                    {profit_factor_display}")
        print(f"Total Number of Trades:           {stats['# Trades']}")
        
        all_trades = stats['_trades']
        # PnL from backtesting.py with size=1 is effectively in points for NIFTY.
        print(f"Average Trade PnL (points):       {all_trades['PnL'].mean():.2f}")
        
        winning_trades = all_trades[all_trades['PnL'] > 0]
        losing_trades = all_trades[all_trades['PnL'] < 0]
        
        avg_winning_trade_points = winning_trades['PnL'].mean() if not winning_trades.empty else 0
        avg_losing_trade_points = losing_trades['PnL'].mean() if not losing_trades.empty else 0
        
        print(f"Average Winning Trade (points):   {avg_winning_trade_points:.2f}")
        print(f"Average Losing Trade (points):    {avg_losing_trade_points:.2f}")
        
        avg_trade_duration_bars = all_trades['Duration'].mean()
        # Duration might be Timedelta; print without float formatting if so, or convert if needed.
        # For now, direct print is safer. If it's numeric, it will show. If Timedelta, it has its own str form.
        print(f"Average Trade Duration (bars):    {avg_trade_duration_bars} (raw mean of Duration column)")
    else:
        print("\nNo trades were executed or stats are unavailable.")

    # Plot the results (optional, will open in browser if not running in a headless environment)
    # print("\nPlotting results... (this may take a moment and open a browser window)")
    # bt.plot()
